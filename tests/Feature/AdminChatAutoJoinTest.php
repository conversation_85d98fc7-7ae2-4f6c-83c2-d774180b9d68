<?php

namespace Tests\Feature;

use App\Events\AdminRoleAssignedEvent;
use App\Listeners\AdminRoleAssignedListener;
use App\Models\Chat;
use App\Models\User;
use App\Services\ChatService;
use App\Enums\RoleEnum;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class AdminChatAutoJoinTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create roles if they don't exist
        Role::firstOrCreate(['name' => RoleEnum::ADMIN, 'guard_name' => 'web']);
        Role::firstOrCreate(['name' => RoleEnum::SUPREME_ADMIN, 'guard_name' => 'web']);
        Role::firstOrCreate(['name' => RoleEnum::CONSUMER, 'guard_name' => 'web']);
    }

    /** @test */
    public function it_adds_new_admin_to_existing_admin_chats()
    {
        // Create existing admin users
        $admin1 = User::factory()->create();
        $admin1->assignRole(RoleEnum::ADMIN);
        
        $admin2 = User::factory()->create();
        $admin2->assignRole(RoleEnum::ADMIN);
        
        // Create a regular user
        $user = User::factory()->create();
        $user->assignRole(RoleEnum::CONSUMER);
        
        // Create an admin chat group
        $chatService = app(ChatService::class);
        $adminChat = Chat::create([
            'type' => 'group',
            'name' => 'Support Chat - Test User',
            'description' => 'Group chat with all administrators for Test User',
            'participants' => [$user->id, $admin1->id, $admin2->id],
            'created_by' => $user->id,
        ]);
        
        // Create a new admin user
        $newAdmin = User::factory()->create();
        
        // Test the addAdminToExistingChats method directly
        $newAdmin->assignRole(RoleEnum::ADMIN);
        $updatedCount = $chatService->addAdminToExistingChats($newAdmin->id);
        
        // Assert the new admin was added to the chat
        $this->assertEquals(1, $updatedCount);
        
        $updatedChat = Chat::find($adminChat->id);
        $this->assertContains($newAdmin->id, $updatedChat->participants);
        $this->assertCount(4, $updatedChat->participants); // user + 3 admins
    }

    /** @test */
    public function it_fires_event_when_admin_role_assigned()
    {
        Event::fake();
        
        $user = User::factory()->create();
        
        // Simulate role assignment that would trigger the event
        event(new AdminRoleAssignedEvent($user));
        
        Event::assertDispatched(AdminRoleAssignedEvent::class, function ($event) use ($user) {
            return $event->user->id === $user->id;
        });
    }

    /** @test */
    public function it_handles_listener_correctly()
    {
        // Create existing admin chat
        $admin1 = User::factory()->create();
        $admin1->assignRole(RoleEnum::ADMIN);
        
        $user = User::factory()->create();
        $user->assignRole(RoleEnum::CONSUMER);
        
        $adminChat = Chat::create([
            'type' => 'group',
            'name' => 'Support Chat - Test User',
            'description' => 'Group chat with all administrators for Test User',
            'participants' => [$user->id, $admin1->id],
            'created_by' => $user->id,
        ]);
        
        // Create new admin and trigger event
        $newAdmin = User::factory()->create();
        $newAdmin->assignRole(RoleEnum::ADMIN);
        
        $listener = app(AdminRoleAssignedListener::class);
        $event = new AdminRoleAssignedEvent($newAdmin);
        
        $listener->handle($event);
        
        // Check if admin was added to chat
        $updatedChat = Chat::find($adminChat->id);
        $this->assertContains($newAdmin->id, $updatedChat->participants);
    }
}
