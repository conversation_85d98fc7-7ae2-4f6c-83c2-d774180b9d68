<?php

namespace Modules\Subscription\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Modules\Subscription\Repositories\API\SubscriptionRepository;
use Stripe\Stripe;
use Exception;
use App\Exceptions\ExceptionHandler;
use App\Helpers\Helpers;
use Modules\Subscription\Entities\Plan;

class StripeController extends Controller
{
    protected $repository;
    protected $stripe;

    public function __construct(SubscriptionRepository $repository)
    {
        $this->repository = $repository;
        Stripe::setApiKey(config('services.stripe.secret'));
    }

    public function createCheckoutSession(Request $request)
    {
        try {
            $request->validate([
                'planId' => 'required|exists:plans,id',
                'successUrl' => 'required|url',
                'cancelUrl' => 'required|url'
            ]);

            $user = auth()->user();
            $plan = Plan::findOrFail($request->planId);

            if (!$plan->stripe_price_id || !$plan->stripe_product_id) {
                throw new Exception('Invalid Stripe configuration for this plan', 400);
            }

            $successUrl = $request->successUrl;
            if (!Str::contains($successUrl, '{CHECKOUT_SESSION_ID}')) {
                $separator = parse_url($successUrl, PHP_URL_QUERY) ? '&' : '?';
                $successUrl .= $separator . 'session_id={CHECKOUT_SESSION_ID}';
            }

            $session = \Stripe\Checkout\Session::create([
                'payment_method_types' => ['card'],
                'line_items' => [[
                    'price' => $plan->stripe_price_id,
                    'quantity' => 1,
                ]],
                'mode' => 'subscription',
                'success_url' => $successUrl,
                'cancel_url' => $request->cancelUrl,
                'client_reference_id' => $user->id,
                'customer_email' => $user->email,
                'subscription_data' => [
                    'metadata' => [
                        'userId' => $user->id,
                        'planId' => $plan->id,
                        'productId' => $plan->stripe_product_id
                    ]
                ],
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'sessionId' => $session->id,
                    'url' => $session->url
                ]
            ]);

        } catch (\Stripe\Exception\ApiErrorException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Stripe error: ' . $e->getMessage(),
            ], 400);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], $e->getCode() ?: 400);
        }
    }

    public function verifySession($sessionId)
    {
        try {
            $session = \Stripe\Checkout\Session::retrieve($sessionId);
            
            // Verify the session belongs to the authenticated user
            if ($session->client_reference_id != Helpers::getCurrentUserId()) {
                throw new Exception('Unauthorized access to this session', 403);
            }

            $data = [
                'success' => $session->payment_status === 'paid',
                'data' => [
                    'payment_status' => $session->payment_status,
                    'subscription_status' => $session->subscription ?? null,
                    'customer' => $session->customer ?? null,
                ]
            ];

            if ($session->payment_status === 'paid') {
                $data['message'] = 'Payment verified successfully';
            } else {
                $data['message'] = 'Payment not completed';
            }

            return response()->json($data);

        } catch (Exception $e) {
            throw new ExceptionHandler($e->getMessage(), $e->getCode() ?: 400);
        }
    }
}