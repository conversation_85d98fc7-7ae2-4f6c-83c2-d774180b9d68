<?php

namespace App\Repositories\API;

use App\Enums\RoleEnum;
use App\Exceptions\ExceptionHandler;
use App\Models\Address;
use App\Models\User;
use App\Models\Business;
use Exception;
use Illuminate\Support\Facades\DB;
use Prettus\Repository\Eloquent\BaseRepository;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\Log;

class UserRepository extends BaseRepository
{
    protected $fieldSearchable = [
        'name' => 'like',
    ];

    protected $role;

    protected $address;

    public function model()
    {
        $this->address = new Address();
        $this->role = new Role();

        return User::class;
    }

    public function getAllUsers()
    {
        DB::beginTransaction();
        try {
            return $this->model->role('user')->with('addresses');
        } catch (Exception $e) {
            throw new ExceptionHandler($e->getMessage(), $e->getCode());
        }
    }

    public function destroy($id)
    {
        try {
            $user = $this->model->findOrFail($id);
            if ($user->hasRole(RoleEnum::ADMIN)) {
                throw new Exception(__('static.users.reserved_user_not_deleted'), 400);
            }
            return $user->update(['deleted_at' => now()]);
        } catch (Exception $e) {
            throw new ExceptionHandler($e->getMessage(), $e->getCode());
        }
    }

    public function countActiveByRole($roleName)
    {
        try {
            return $this->model->whereHas('roles', function($q) use ($roleName) {
                $q->where('name', $roleName);
            })->count();
        } catch (Exception $e) {
            throw new ExceptionHandler($e->getMessage(), $e->getCode());
        }
    }

    public function getCustomers($status = null, $perPage = 15)
    {
        try {
            $query = $this->model->role('user')->where('system_reserve', false);
            if (!is_null($status)) {
                $query->where('status', $status);
            }
            return $query->orderByDesc('created_at')->paginate($perPage);
        } catch (Exception $e) {
            throw new ExceptionHandler($e->getMessage(), $e->getCode());
        }
    }

    public function countActiveProvidersFromBusiness()
    {
        try {
            return Business::count();
        } catch (Exception $e) {
            throw new ExceptionHandler($e->getMessage(), $e->getCode());
        }
    }

    public function update(array $attributes, $id)
    {
        $user = $this->model->findOrFail($id);
        DB::beginTransaction();
        try {
            if (isset($attributes['password'])) {
                $attributes['password'] = bcrypt($attributes['password']);
            }
            $user->update($attributes);
            DB::commit();
            return $user;
        } catch (Exception $e) {
            DB::rollBack();
            throw new ExceptionHandler($e->getMessage(), $e->getCode());
        }
    }

    public function getList($request)
    {
        try {
            $query = $this->model->query();
            
            // Filter by role if role_id is provided
            if ($request->filled('role_id')) {
                $role = \Spatie\Permission\Models\Role::find($request->input('role_id'));
                if ($role) {
                    $query->role($role->name);
                }
            }
            
            // Search functionality
            if ($request->filled('search')) {
                $searchTerm = $request->input('search');
                $query->where(function($q) use ($searchTerm) {
                    // Search in basic user fields
                    $q->where('name', 'like', "%{$searchTerm}%")
                      ->orWhere('email', 'like', "%{$searchTerm}%")
                      ->orWhere('phone', 'like', "%{$searchTerm}%")
                      ->orWhere('slug', 'like', "%{$searchTerm}%");
                });
            }
            
            return $query->orderByDesc('created_at')->paginate($request->input('per_page', 15));
        } catch (Exception $e) {
            throw new ExceptionHandler($e->getMessage(), $e->getCode());
        }
    }

    public function getShow($id)
    {
        try {
            Log::info('UserRepository@getShow called', ['id' => $id]);
            $user = $this->model->findOrFail($id);
            Log::info('UserRepository@getShow result', ['user_id' => $user->id, 'certificates' => $user->certificates, 'certificates_status' => $user->certificates_status]);
            return $user;
        } catch (Exception $e) {
            Log::error('UserRepository@getShow error', ['id' => $id, 'error' => $e->getMessage()]);
            throw new ExceptionHandler($e->getMessage(), $e->getCode());
        }
    }

    public function suspendProvider($id)
    {
        try {
            $user = $this->model->findOrFail($id);
            if (!$user->hasRole('provider')) {
                throw new Exception(__('User is not a provider'), 400);
            }
            if ($user->status === 0) {
                throw new Exception(__('Provider already suspended'), 400);
            }
            $user->update(['status' => 0]);
            return $user;
        } catch (Exception $e) {
            throw new ExceptionHandler($e->getMessage(), $e->getCode());
        }
    }

    public function unsuspendProvider($id)
    {
        try {
            $user = $this->model->findOrFail($id);
            if (!$user->hasRole('provider')) {
                throw new Exception(__('User is not a provider'), 400);
            }
            if ($user->status === 1) {
                throw new Exception(__('Provider is already active'), 400);
            }
            $user->update(['status' => 1]);
            return $user;
        } catch (Exception $e) {
            throw new ExceptionHandler($e->getMessage(), $e->getCode());
        }
    }
}