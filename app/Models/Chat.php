<?php

namespace App\Models;

use MongoDB\Laravel\Eloquent\Model;

class Chat extends Model
{
    protected $connection = 'mongodb';
    protected $collection = 'chats';

    protected $fillable = [
        'type',
        'participants',
        'name',
        'description',
        'created_by',
    ];

    public function setParticipantsAttribute($value)
    {
        $this->attributes['participants'] = array_map('intval', $value);
    }

    public function getParticipantsAttribute($value)
    {
        $arr = is_string($value) ? json_decode($value, true) : $value;
        return array_map('intval', $arr);
    }

    public function messages()
    {
        return $this->hasMany(Message::class, 'chat_id');
    }

    public function getParticipantsDataAttribute()
    {
        if (empty($this->participants)) {
            return collect();
        }
        return \App\Models\User::whereIn('id', $this->participants)->get();
    }

    public function creator()
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    public function isGroupChat()
    {
        return $this->type === 'group';
    }

    public function isPrivateChat()
    {
        return $this->type === 'private';
    }

    public function getDisplayNameAttribute()
    {
        if ($this->isGroupChat() && $this->name) {
            return $this->name;
        }

        if ($this->isPrivateChat() && count($this->participants) === 2) {
            $currentUserId = auth()->id();
            $otherParticipantId = collect($this->participants)->first(function ($id) use ($currentUserId) {
                return $id != $currentUserId;
            });

            if ($otherParticipantId) {
                $otherUser = \App\Models\User::find($otherParticipantId);
                return $otherUser ? $otherUser->name : 'Unknown User';
            }
        }

        return 'Chat';
    }

    public function getUnreadCountForUser($userId)
    {
        return \App\Models\Message::where('chat_id', $this->_id)
            ->where('sender_id', '!=', $userId)
            ->where(function($query) use ($userId) {
                $query->where('read_by', 'not in', [$userId])
                      ->orWhereNull('read_by');
            })
            ->count();
    }
}