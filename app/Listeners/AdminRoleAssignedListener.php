<?php

namespace App\Listeners;

use App\Events\AdminRoleAssignedEvent;
use App\Services\ChatService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class AdminRoleAssignedListener implements ShouldQueue
{
    use InteractsWithQueue;

    protected $chatService;

    /**
     * Create the event listener.
     */
    public function __construct(ChatService $chatService)
    {
        $this->chatService = $chatService;
    }

    /**
     * Handle the event.
     */
    public function handle(AdminRoleAssignedEvent $event)
    {
        try {
            // Add the new admin to all existing admin chat groups
            $updatedChatsCount = $this->chatService->addAdminToExistingChats($event->user->id);
            
            Log::info("Admin role assigned to user {$event->user->id} ({$event->user->name}). Added to {$updatedChatsCount} admin chat groups.");
            
        } catch (\Exception $e) {
            Log::error("Failed to add new admin {$event->user->id} to existing chat groups: " . $e->getMessage());
        }
    }
}
