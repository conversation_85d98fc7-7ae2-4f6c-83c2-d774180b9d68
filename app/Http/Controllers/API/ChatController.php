<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Http;
use App\Services\ChatService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Chat;
use App\Models\Message;
use App\Events\MessageSent;
use App\Http\Requests\API\CreateChatByBusinessRequest;
use MongoDB\BSON\ObjectId;

class ChatController extends Controller
{
    protected $service;

    public function __construct(ChatService $service)
    {
        $this->middleware('auth:api');
        $this->service = $service;
    }

    public function index()
    {
        $userId = (int) Auth::id();
    
        $chats = Chat::where('participants', $userId)->get();
    
        $chats->each(function ($chat) {
            $chat->last_message = Message::where('chat_id', new ObjectId((string) $chat->_id))
                ->latest()
                ->first();
        });
    
        return \App\Http\Resources\ChatResource::collection($chats);
    }
    
    public function messages(Request $request, $chatId)
    {
        $search = $request->query('search');
        $perPage = $request->query('per_page', 20);
        $messages = $this->service->getMessages($chatId, $search, $perPage);
        return response()->json($messages);
    }

    public function sendMessage(Request $request, $chatId)
    {
        $data = $request->validate([
            'type' => 'nullable|string|in:text,image,file',
            'message' => 'nullable|string',
            'media_url' => 'nullable|url',
        ]);
        $message = $this->service->sendMessage($chatId, $data);

        try {
            Http::post('http://localhost:8081/broadcast', [
                'chat_id' => $chatId,
                'message' => $message->toArray(),
            ]);
        } catch (\Exception $e) {
            \Log::error('WebSocket broadcast failed: ' . $e->getMessage());
        }

        return response()->json($message, 200);
    }

    public function createChat(Request $request)
    {
        $data = $request->validate([
            'user_id' => 'required|integer|exists:users,id',
        ]);

        if ($this->service->isAdmin($data['user_id'])) {
            $chat = $this->service->getOrCreateAdminChat($data['user_id']);
        } else {
            $chat = $this->service->getOrCreateChat($data['user_id']);
        }

        return response()->json($chat, 200);
    }

    /**
     * Create a chat with a provider based on business UUID
     *
     * @param CreateChatByBusinessRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createChatByBusiness(CreateChatByBusinessRequest $request)
    {
        try {
            $chat = $this->service->getOrCreateChatByBusinessUuid($request->business_uuid);
            return response()->json($chat, 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], $e->getCode() ?: 500);
        }
    }

 
    public function createGroupChat(Request $request)
    {
        $data = $request->validate([
            'participant_ids' => 'required|array|min:1',
            'participant_ids.*' => 'integer|exists:users,id',
            'name' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:1000',
        ]);

        try {
            $chat = $this->service->createGroupChat(
                $data['participant_ids'],
                $data['name'] ?? null,
                $data['description'] ?? null
            );
            return response()->json($chat, 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function createAdminChat(Request $request)
    {
        $data = $request->validate([
            'admin_id' => 'nullable|integer|exists:users,id',
        ]);

        try {
            $chat = $this->service->getOrCreateAdminChat($data['admin_id'] ?? null);
            return response()->json($chat, 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], $e->getCode() ?: 500);
        }
    }

    public function markAsRead($chatId)
    {
        $this->service->markAsRead($chatId);
        return response()->json(['status' => 'ok']);
    }

    public function deleteMessage($chatId, $messageId)
    {
        $this->service->deleteMessage($messageId);
        return response()->json(['status' => 'deleted']);
    }
}